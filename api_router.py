"""
REST API 模块（使用FastAPI实现）
增强版本：包含完整的CRUD操作、错误处理、性能优化和安全性增强
"""
from fastapi import FastAPI, UploadFile, File, HTTPException, BackgroundTasks, Depends, Security, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from pydantic import BaseModel, Field, field_validator
import tempfile
import os
import re
from typing import Dict, Any, List, Optional, Union
import logging
import json
import asyncio
from contextlib import asynccontextmanager
import time
import hashlib
from datetime import datetime, timedelta
import uuid

# 从rag_demo导入所需功能
try:
    import rag_demo_pro as rag_demo
except ImportError:
    import rag_demo
from io import StringIO

# 配置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("rag-api")

# 限流器配置（简化版本，不依赖slowapi）
class SimpleLimiter:
    def __init__(self):
        self.requests = {}
        self.window_size = 60  # 60秒窗口
        self.max_requests = 100  # 每分钟最多100个请求

    def is_allowed(self, client_ip: str) -> bool:
        now = time.time()
        if client_ip not in self.requests:
            self.requests[client_ip] = []

        # 清理过期请求
        self.requests[client_ip] = [
            req_time for req_time in self.requests[client_ip]
            if now - req_time < self.window_size
        ]

        # 检查是否超过限制
        if len(self.requests[client_ip]) >= self.max_requests:
            return False

        # 记录当前请求
        self.requests[client_ip].append(now)
        return True

limiter = SimpleLimiter()

# API密钥配置
API_KEYS = {
    "demo-key-123": "demo_user",
    "admin-key-456": "admin_user"
}

# 安全认证
security = HTTPBearer(auto_error=False)

def get_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> Optional[str]:
    """验证API密钥"""
    if not credentials:
        return None

    api_key = credentials.credentials
    if api_key in API_KEYS:
        return API_KEYS[api_key]
    return None

def require_api_key(user: str = Depends(get_api_key)) -> str:
    """要求API密钥认证"""
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API密钥",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

# 错误响应模型
class ErrorResponse(BaseModel):
    """统一错误响应格式"""
    error: bool = True
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误信息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误发生时间")
    request_id: Optional[str] = Field(None, description="请求ID")

# 定义类用于模拟Gradio进度回调
class ProgressCallback:
    def __init__(self):
        self.progress = 0
        self.description = ""

    def __call__(self, progress: float, desc: str = None):
        self.progress = progress
        self.description = desc or ""
        logger.info(f"进度: {progress:.2f} - {desc}")
        return self

# 扩展的数据模型
class DocumentInfo(BaseModel):
    """文档信息模型"""
    id: str = Field(..., description="文档唯一标识")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    upload_time: datetime = Field(..., description="上传时间")
    chunks_count: int = Field(..., description="分块数量")
    status: str = Field(..., description="处理状态")
    file_hash: Optional[str] = Field(None, description="文件哈希值")

class ChunkInfo(BaseModel):
    """分块信息模型"""
    id: str = Field(..., description="分块唯一标识")
    document_id: str = Field(..., description="所属文档ID")
    content: str = Field(..., description="分块内容")
    position: int = Field(..., description="在文档中的位置")
    char_count: int = Field(..., description="字符数")
    token_count: int = Field(..., description="分词数")

class SystemConfig(BaseModel):
    """系统配置模型"""
    chunk_size: int = Field(800, description="分块大小", ge=100, le=2000)
    chunk_overlap: int = Field(150, description="分块重叠", ge=0, le=500)
    max_iterations: int = Field(3, description="递归检索最大迭代次数", ge=1, le=10)
    rerank_method: str = Field("cross_encoder", description="重排序方法")
    enable_web_search: bool = Field(False, description="是否启用网络搜索")
    model_choice: str = Field("ollama", description="模型选择")

    @field_validator('rerank_method')
    @classmethod
    def validate_rerank_method(cls, v):
        if v not in ['cross_encoder', 'llm', 'none']:
            raise ValueError('重排序方法必须是 cross_encoder, llm 或 none')
        return v

    @field_validator('model_choice')
    @classmethod
    def validate_model_choice(cls, v):
        if v not in ['ollama', 'siliconflow']:
            raise ValueError('模型选择必须是 ollama 或 siliconflow')
        return v

class DocumentListResponse(BaseModel):
    """文档列表响应模型"""
    documents: List[DocumentInfo] = Field(..., description="文档列表")
    total: int = Field(..., description="文档总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")

class ChunkListResponse(BaseModel):
    """分块列表响应模型"""
    chunks: List[ChunkInfo] = Field(..., description="分块列表")
    total: int = Field(..., description="分块总数")
    document_id: Optional[str] = Field(None, description="文档ID过滤")

class ConfigResponse(BaseModel):
    """配置响应模型"""
    config: SystemConfig = Field(..., description="系统配置")
    last_updated: datetime = Field(..., description="最后更新时间")

# 启动时确保模型和向量存储准备就绪
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 检查环境
    if not rag_demo.check_environment():
        logger.error("环境检查失败！请确保Ollama服务已启动且所需模型已加载")
    yield
    # 清理工作（如果需要）
    logger.info("API服务已关闭")

# 初始化FastAPI应用
app = FastAPI(
    title="本地RAG API服务",
    description="提供基于本地大模型和SERPAPI的文档问答API接口",
    version="1.0.0",
    lifespan=lifespan
)

# 允许跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class QuestionRequest(BaseModel):
    question: str
    enable_web_search: bool = False

class AnswerResponse(BaseModel):
    answer: str
    sources: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class FileProcessResult(BaseModel):
    status: str
    message: str
    file_info: Optional[Dict[str, Any]] = None

async def process_answer_stream(question: str, enable_web_search: bool):
    """处理流式回答，模拟同步函数的异步版本"""
    progress = ProgressCallback()
    answer = ""
    
    # 创建生成器函数的包装器
    def run_stream():
        for response, status in rag_demo.stream_answer(question, enable_web_search, progress):
            nonlocal answer
            answer = response
            yield response, status
    
    # 在异步上下文中运行同步代码
    loop = asyncio.get_event_loop()
    generator = run_stream()
    
    # 消费生成器直到最后一个结果
    try:
        while True:
            resp, status = await loop.run_in_executor(None, next, generator)
            if status == "完成!":
                break
    except StopIteration:
        pass
    
    return answer

@app.post("/api/upload", response_model=FileProcessResult)
async def upload_pdf(file: UploadFile = File(...)):
    """
    处理PDF文档并存入向量数据库
    - 支持格式：application/pdf
    - 最大文件大小：50MB
    """
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(400, "仅支持PDF文件")

    try:
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            content = await file.read()
            tmp.write(content)
            tmp_path = tmp.name

        # 创建一个进度回调
        progress = ProgressCallback()
        
        # 使用rag_demo中的处理函数
        result_text = await asyncio.to_thread(
            rag_demo.process_multiple_pdfs,
            [type('obj', (object,), {"name": tmp_path})],
            progress
        )
        
        # 清理临时文件
        os.unlink(tmp_path)
        
        # 解析结果
        result = result_text[0] if isinstance(result_text, tuple) else result_text
        chunk_match = re.search(r'(\d+) 个文本块', result)
        chunks = int(chunk_match.group(1)) if chunk_match else 0
        
        return {
            "status": "success" if "成功" in result else "error",
            "message": result,
            "file_info": {
                "filename": file.filename,
                "chunks": chunks
            }
        }
    except Exception as e:
        logger.error(f"PDF处理失败: {str(e)}")
        raise HTTPException(500, f"文档处理失败: {str(e)}") from e

@app.post("/api/ask", response_model=AnswerResponse)
async def ask_question(req: QuestionRequest):
    """
    问答接口
    - question: 问题内容
    - enable_web_search: 是否启用网络搜索增强（默认False）
    """
    if not req.question:
        raise HTTPException(400, "问题不能为空")
    
    try:
        # 使用流式回答生成结果
        answer = await process_answer_stream(req.question, req.enable_web_search)
        
        # 提取可能的来源信息
        sources = []
        
        # 尝试提取标记的URL内容
        url_matches = re.findall(r'\[(网络来源|本地文档):[^\]]+\]\s*(?:\(URL:\s*([^)]+)\))?', answer)
        for source_type, url in url_matches:
            if url:
                sources.append({"type": source_type, "url": url})
            else:
                sources.append({"type": source_type})
        
        # 如果没有找到标记的URL，尝试解析其他格式
        if not sources:
            if "来源:" in answer or "来源：" in answer:
                source_sections = re.findall(r'来源[:|：](.*?)(?=来源[:|：]|$)', answer, re.DOTALL)
                for section in source_sections:
                    sources.append({"type": "引用", "content": section.strip()})
        
        return {
            "answer": answer,
            "sources": sources,
            "metadata": {
                "enable_web_search": req.enable_web_search,
                "model": "deepseek-r1:1.5b"
            }
        }
    except Exception as e:
        logger.error(f"问答失败: {str(e)}")
        raise HTTPException(500, f"问答处理失败: {str(e)}") from e

@app.get("/api/status")
async def check_status():
    """检查API服务状态和环境配置"""
    ollama_status = rag_demo.check_environment()
    serpapi_status = rag_demo.check_serpapi_key()
    
    return {
        "status": "healthy" if ollama_status else "degraded",
        "ollama_service": ollama_status,
        "serpapi_configured": serpapi_status,
        "version": "1.0.0",
        "models": ["deepseek-r1:1.5b", "deepseek-r1:7b"]
    }

@app.get("/api/web_search_status")
async def check_web_search():
    """检查网络搜索功能是否可用"""
    serpapi_key = rag_demo.SERPAPI_KEY
    return {
        "web_search_available": bool(serpapi_key and serpapi_key.strip()),
        "serpapi_configured": bool(serpapi_key and serpapi_key.strip())
    }

if __name__ == "__main__":
    import uvicorn
    port = 17995
    
    # 尝试使用rag_demo中的端口检测逻辑
    ports = [17995, 17996, 17997, 17998, 17999]
    for p in ports:
        if rag_demo.is_port_available(p):
            port = p
            break
    
    logger.info(f"正在启动API服务，端口: {port}")
    uvicorn.run(app, host="0.0.0.0", port=port) 