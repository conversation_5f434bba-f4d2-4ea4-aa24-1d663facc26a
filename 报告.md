# 智能测试平台数据存储架构设计方案

## 1. 系统架构概览

### 1.1 整体架构图

```mermaid
graph TB
    A[需求文档输入] --> B[文档预处理层]
    B --> C[智能分块引擎]
    C --> D[向量化处理]
    D --> E[混合索引层]
    E --> F[FAISS向量索引]
    E --> G[BM25关键词索引]
    E --> H[元数据存储]
    
    I[查询接口] --> J[混合检索引擎]
    J --> F
    J --> G
    J --> H
    J --> K[重排序模块]
    K --> L[测试用例生成]
    K --> M[需求覆盖分析]
    
    H --> N[PostgreSQL]
    H --> O[Redis缓存]
```

### 1.2 核心组件说明

- **文档预处理层**：文档格式转换、清洗、结构化提取
- **智能分块引擎**：基于语义的文档分块策略
- **混合索引层**：FAISS + BM25 + 元数据的三层索引架构
- **混合检索引擎**：语义检索与关键词检索的融合
- **重排序模块**：基于交叉编码器的结果精排

## 2. 需求文档存储方案

### 2.1 文档存储架构

```python
# 文档存储结构设计
class DocumentStorage:
    def __init__(self):
        self.file_storage = FileSystemStorage()  # 原始文件存储
        self.chunk_storage = ChunkStorage()      # 分块存储
        self.metadata_db = PostgreSQLStorage()  # 元数据存储
        
    def store_document(self, doc_file, metadata):
        # 1. 存储原始文件
        file_id = self.file_storage.save(doc_file)
        
        # 2. 智能分块处理
        chunks = self.intelligent_chunking(doc_file)
        
        # 3. 存储分块和关联关系
        chunk_ids = []
        for chunk in chunks:
            chunk_id = self.chunk_storage.save(chunk, file_id)
            chunk_ids.append(chunk_id)
            
        # 4. 存储元数据
        self.metadata_db.save_document_metadata(
            file_id, chunk_ids, metadata
        )
        
        return file_id, chunk_ids
```

### 2.2 智能分块策略

基于当前系统的实践经验，推荐以下分块参数：

```python
class IntelligentChunker:
    def __init__(self):
        # 基于文档类型的动态分块策略
        self.chunk_configs = {
            'requirements_spec': {
                'chunk_size': 800,      # 需求规格书适中分块
                'chunk_overlap': 150,   # 保持上下文连贯性
                'separators': [
                    "\n## ",           # 章节分隔
                    "\n### ",          # 子章节分隔
                    "\n- ",            # 需求项分隔
                    "。",              # 句子分隔
                    "\n\n",            # 段落分隔
                ]
            },
            'test_cases': {
                'chunk_size': 600,
                'chunk_overlap': 100,
                'separators': ["\n测试用例", "\n步骤", "\n预期结果"]
            }
        }
    
    def chunk_document(self, text, doc_type='requirements_spec'):
        config = self.chunk_configs[doc_type]
        
        # 使用RecursiveCharacterTextSplitter
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=config['chunk_size'],
            chunk_overlap=config['chunk_overlap'],
            separators=config['separators']
        )
        
        chunks = splitter.split_text(text)
        
        # 添加位置信息和上下文
        enhanced_chunks = []
        for i, chunk in enumerate(chunks):
            enhanced_chunk = {
                'content': chunk,
                'position': i,
                'start_char': self._calculate_start_position(text, chunk),
                'context_before': chunks[i-1] if i > 0 else "",
                'context_after': chunks[i+1] if i < len(chunks)-1 else ""
            }
            enhanced_chunks.append(enhanced_chunk)
            
        return enhanced_chunks
```

## 3. 数据处理技术选型

### 3.1 文本向量化模型推荐

基于实际测试效果，推荐以下模型配置：

```python
class EmbeddingModelManager:
    def __init__(self):
        # 主要模型：多语言支持，适合中文技术文档
        self.primary_model = SentenceTransformer(
            'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2'
        )
        
        # 备选模型：专门针对中文优化
        self.chinese_model = SentenceTransformer(
            'shibing624/text2vec-base-chinese'
        )
        
        # 技术术语模型：针对IT领域
        self.technical_model = SentenceTransformer(
            'sentence-transformers/all-mpnet-base-v2'
        )
    
    def get_embeddings(self, texts, model_type='primary'):
        model = getattr(self, f'{model_type}_model')
        
        # 批量处理优化
        embeddings = model.encode(
            texts,
            batch_size=32,
            show_progress_bar=True,
            convert_to_numpy=True
        )
        
        return embeddings.astype('float32')  # FAISS优化
```

### 3.2 混合检索实现

```python
class HybridRetriever:
    def __init__(self, alpha=0.7):
        self.alpha = alpha  # 语义检索权重
        self.faiss_index = None
        self.bm25_index = None
        self.cross_encoder = CrossEncoder(
            'sentence-transformers/ms-marco-MiniLM-L-12-v2'
        )
    
    def search(self, query, top_k=10):
        # 1. 语义检索
        semantic_results = self._semantic_search(query, top_k * 2)
        
        # 2. 关键词检索
        keyword_results = self._keyword_search(query, top_k * 2)
        
        # 3. 混合融合
        merged_results = self._hybrid_fusion(
            semantic_results, keyword_results
        )
        
        # 4. 重排序
        reranked_results = self._rerank(query, merged_results[:top_k * 2])
        
        return reranked_results[:top_k]
    
    def _hybrid_fusion(self, semantic_results, keyword_results):
        """基于RRF (Reciprocal Rank Fusion) 的融合策略"""
        score_dict = {}
        
        # 语义检索得分
        for rank, (doc_id, score) in enumerate(semantic_results):
            score_dict[doc_id] = score_dict.get(doc_id, 0) + \
                                self.alpha / (rank + 1)
        
        # 关键词检索得分
        for rank, (doc_id, score) in enumerate(keyword_results):
            score_dict[doc_id] = score_dict.get(doc_id, 0) + \
                                (1 - self.alpha) / (rank + 1)
        
        # 按得分排序
        return sorted(score_dict.items(), key=lambda x: x[1], reverse=True)
```

## 4. 数据模型设计

### 4.1 核心数据表设计

```sql
-- 文档表
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) UNIQUE NOT NULL,
    version INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- 文档分块表
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    start_position INTEGER,
    end_position INTEGER,
    token_count INTEGER,
    embedding_vector VECTOR(384),  -- 使用pgvector扩展
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- 需求项表
CREATE TABLE requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    requirement_id VARCHAR(100) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    priority VARCHAR(20),
    category VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    parent_requirement_id UUID REFERENCES requirements(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- 需求依赖关系表
CREATE TABLE requirement_dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_requirement_id UUID REFERENCES requirements(id),
    target_requirement_id UUID REFERENCES requirements(id),
    dependency_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(source_requirement_id, target_requirement_id, dependency_type)
);

-- 测试用例表
CREATE TABLE test_cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_case_id VARCHAR(100) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    preconditions TEXT,
    test_steps JSONB,
    expected_results TEXT,
    priority VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- 需求覆盖矩阵表
CREATE TABLE requirement_coverage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID REFERENCES requirements(id),
    test_case_id UUID REFERENCES test_cases(id),
    coverage_type VARCHAR(50),
    confidence_score FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(requirement_id, test_case_id)
);
```

### 4.2 索引优化策略

```sql
-- 性能优化索引
CREATE INDEX idx_documents_hash ON documents(file_hash);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_chunks_embedding ON document_chunks USING ivfflat (embedding_vector vector_cosine_ops);
CREATE INDEX idx_requirements_document ON requirements(document_id);
CREATE INDEX idx_requirements_parent ON requirements(parent_requirement_id);
CREATE INDEX idx_coverage_requirement ON requirement_coverage(requirement_id);
CREATE INDEX idx_coverage_testcase ON requirement_coverage(test_case_id);

-- JSONB字段索引
CREATE INDEX idx_documents_metadata ON documents USING gin(metadata);
CREATE INDEX idx_chunks_metadata ON document_chunks USING gin(metadata);

```
## 5. 数据库技术选择对比

### 5.1 向量数据库对比分析

| 特性 | FAISS | Milvus | Qdrant | Pinecone |
|------|-------|--------|--------|----------|
| **部署方式** | 本地内存 | 分布式 | 本地/云端 | 云端SaaS |
| **性能** | 极高 | 高 | 高 | 高 |
| **可扩展性** | 有限 | 优秀 | 良好 | 优秀 |
| **维护成本** | 低 | 中等 | 低 | 极低 |
| **数据持久化** | 需自实现 | 内置 | 内置 | 内置 |
| **实时更新** | 困难 | 支持 | 支持 | 支持 |
| **成本** | 免费 | 开源/商业 | 开源/商业 | 按使用付费 |

### 5.2 推荐方案

**阶段一（MVP）**：FAISS + PostgreSQL + Redis
- 快速原型验证
- 成本可控
- 技术栈简单

**阶段二（生产）**：Qdrant + PostgreSQL + Redis
- 支持实时更新
- 良好的可扩展性
- 开源可控

**阶段三（企业级）**：Milvus + PostgreSQL + Redis
- 分布式架构
- 企业级特性
- 高可用性

### 5.3 具体实现方案

```python
class VectorDatabaseManager:
    def __init__(self, db_type='faiss'):
        self.db_type = db_type
        if db_type == 'faiss':
            self.vector_db = FAISSDatabase()
        elif db_type == 'qdrant':
            self.vector_db = QdrantDatabase()
        elif db_type == 'milvus':
            self.vector_db = MilvusDatabase()

    def create_collection(self, collection_name, dimension=384):
        if self.db_type == 'qdrant':
            from qdrant_client import QdrantClient
            from qdrant_client.models import Distance, VectorParams

            client = QdrantClient("localhost", port=6333)
            client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=dimension,
                    distance=Distance.COSINE
                )
            )
        elif self.db_type == 'milvus':
            from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType

            connections.connect("default", host="localhost", port="19530")

            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=dimension),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535)
            ]

            schema = CollectionSchema(fields, "智能测试平台向量集合")
            collection = Collection(collection_name, schema)

            # 创建索引
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            collection.create_index("embedding", index_params)

class FAISSDatabase:
    def __init__(self):
        self.index = None
        self.id_mapping = {}
        self.content_mapping = {}

    def add_vectors(self, vectors, ids, contents):
        if self.index is None:
            dimension = vectors.shape[1]
            self.index = faiss.IndexFlatIP(dimension)  # 内积相似度

        # 标准化向量（用于余弦相似度）
        faiss.normalize_L2(vectors)

        self.index.add(vectors)

        # 更新映射
        start_idx = len(self.id_mapping)
        for i, (doc_id, content) in enumerate(zip(ids, contents)):
            self.id_mapping[start_idx + i] = doc_id
            self.content_mapping[doc_id] = content

    def search(self, query_vector, top_k=10):
        faiss.normalize_L2(query_vector.reshape(1, -1))
        scores, indices = self.index.search(query_vector.reshape(1, -1), top_k)

        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx != -1:  # 有效索引
                doc_id = self.id_mapping[idx]
                content = self.content_mapping[doc_id]
                results.append((doc_id, float(score), content))

        return results

```
## 6. 架构优化策略

### 6.1 缓存层设计

```python
class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.local_cache = {}
        self.cache_ttl = 3600  # 1小时

    def get_embeddings(self, text_hash):
        """获取文本嵌入向量缓存"""
        cache_key = f"embedding:{text_hash}"

        # 先查本地缓存
        if cache_key in self.local_cache:
            return self.local_cache[cache_key]

        # 再查Redis缓存
        cached_embedding = self.redis_client.get(cache_key)
        if cached_embedding:
            embedding = np.frombuffer(cached_embedding, dtype=np.float32)
            self.local_cache[cache_key] = embedding
            return embedding

        return None

    def set_embeddings(self, text_hash, embedding):
        """设置文本嵌入向量缓存"""
        cache_key = f"embedding:{text_hash}"

        # 存储到Redis
        self.redis_client.setex(
            cache_key,
            self.cache_ttl,
            embedding.tobytes()
        )

        # 存储到本地缓存
        self.local_cache[cache_key] = embedding

    def get_search_results(self, query_hash, params_hash):
        """获取搜索结果缓存"""
        cache_key = f"search:{query_hash}:{params_hash}"
        cached_results = self.redis_client.get(cache_key)

        if cached_results:
            return json.loads(cached_results)
        return None

    def set_search_results(self, query_hash, params_hash, results):
        """设置搜索结果缓存"""
        cache_key = f"search:{query_hash}:{params_hash}"
        self.redis_client.setex(
            cache_key,
            300,  # 5分钟缓存
            json.dumps(results, ensure_ascii=False)
        )

```
### 6.2 增量更新策略

```python
class IncrementalUpdater:
    def __init__(self, vector_db, metadata_db):
        self.vector_db = vector_db
        self.metadata_db = metadata_db
        self.update_queue = Queue()
        self.batch_size = 100

    def update_document(self, document_id, new_content):
        """增量更新文档"""
        # 1. 获取旧的分块
        old_chunks = self.metadata_db.get_chunks_by_document(document_id)

        # 2. 生成新的分块
        new_chunks = self.chunk_document(new_content)

        # 3. 计算差异
        chunks_to_add, chunks_to_update, chunks_to_delete = \
            self._calculate_chunk_diff(old_chunks, new_chunks)

        # 4. 批量更新
        self._batch_update_chunks(
            chunks_to_add, chunks_to_update, chunks_to_delete
        )

    def _calculate_chunk_diff(self, old_chunks, new_chunks):
        """计算分块差异"""
        old_hashes = {chunk['content_hash']: chunk for chunk in old_chunks}
        new_hashes = {self._hash_content(chunk['content']): chunk
                     for chunk in new_chunks}

        # 需要添加的分块
        chunks_to_add = [
            chunk for hash_val, chunk in new_hashes.items()
            if hash_val not in old_hashes
        ]

        # 需要删除的分块
        chunks_to_delete = [
            chunk for hash_val, chunk in old_hashes.items()
            if hash_val not in new_hashes
        ]

        # 需要更新的分块（位置信息变化）
        chunks_to_update = []
        for hash_val, new_chunk in new_hashes.items():
            if hash_val in old_hashes:
                old_chunk = old_hashes[hash_val]
                if (old_chunk['chunk_index'] != new_chunk['position'] or
                    old_chunk['start_position'] != new_chunk['start_char']):
                    chunks_to_update.append((old_chunk, new_chunk))

        return chunks_to_add, chunks_to_update, chunks_to_delete

    def _batch_update_chunks(self, add_chunks, update_chunks, delete_chunks):
        """批量更新分块"""
        # 删除旧分块
        if delete_chunks:
            chunk_ids = [chunk['id'] for chunk in delete_chunks]
            self.vector_db.delete_vectors(chunk_ids)
            self.metadata_db.delete_chunks(chunk_ids)

        # 添加新分块
        if add_chunks:
            embeddings = self._generate_embeddings([c['content'] for c in add_chunks])
            chunk_ids = self.metadata_db.insert_chunks(add_chunks)
            self.vector_db.add_vectors(embeddings, chunk_ids,
                                     [c['content'] for c in add_chunks])

        # 更新现有分块
        for old_chunk, new_chunk in update_chunks:
            self.metadata_db.update_chunk_position(
                old_chunk['id'],
                new_chunk['position'],
                new_chunk['start_char']
            )
```
### 6.3 性能监控与优化

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'search_latency': [],
            'index_size': 0,
            'cache_hit_rate': 0,
            'memory_usage': 0
        }

    def monitor_search_performance(self, func):
        """搜索性能监控装饰器"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()

            latency = end_time - start_time
            self.metrics['search_latency'].append(latency)

            # 记录慢查询
            if latency > 1.0:  # 超过1秒
                logging.warning(f"慢查询检测: {latency:.2f}s, 查询: {args[0]}")

            return result
        return wrapper

    def get_performance_report(self):
        """生成性能报告"""
        if not self.metrics['search_latency']:
            return "暂无性能数据"

        latencies = self.metrics['search_latency']
        report = {
            'avg_latency': np.mean(latencies),
            'p95_latency': np.percentile(latencies, 95),
            'p99_latency': np.percentile(latencies, 99),
            'total_queries': len(latencies),
            'cache_hit_rate': self.metrics['cache_hit_rate'],
            'index_size_mb': self.metrics['index_size'] / (1024 * 1024)
        }

        return report

```
## 7. 智能测试用例生成

### 7.1 基于需求的测试用例生成

```python
class TestCaseGenerator:
    def __init__(self, retriever, llm_client):
        self.retriever = retriever
        self.llm_client = llm_client
        self.test_case_templates = self._load_templates()

    def generate_test_cases(self, requirement_text, requirement_type='functional'):
        """基于需求文本生成测试用例"""
        # 1. 检索相关的历史测试用例
        similar_cases = self.retriever.search(
            f"测试用例 {requirement_text}",
            top_k=5
        )

        # 2. 构建提示词
        prompt = self._build_generation_prompt(
            requirement_text, similar_cases, requirement_type
        )

        # 3. 调用LLM生成
        generated_cases = self.llm_client.generate(prompt)

        # 4. 解析和验证
        parsed_cases = self._parse_test_cases(generated_cases)

        # 5. 质量评估
        quality_scores = self._evaluate_test_case_quality(
            parsed_cases, requirement_text
        )

        return [(case, score) for case, score in zip(parsed_cases, quality_scores)]

    def _build_generation_prompt(self, requirement, similar_cases, req_type):
        """构建测试用例生成提示词"""
        template = f"""
基于以下需求描述，生成详细的测试用例：

需求描述：
{requirement}

需求类型：{req_type}

参考的相似测试用例：
{self._format_similar_cases(similar_cases)}

请生成3-5个测试用例，每个测试用例包含：
1. 测试用例标题
2. 测试目标
3. 前置条件
4. 测试步骤（详细的操作步骤）
5. 预期结果
6. 测试数据（如需要）

输出格式为JSON：
{{
  "test_cases": [
    {{
      "title": "测试用例标题",
      "objective": "测试目标",
      "preconditions": "前置条件",
      "steps": ["步骤1", "步骤2", "步骤3"],
      "expected_result": "预期结果",
      "test_data": "测试数据"
    }}
  ]
}}
"""
        return template

    def _evaluate_test_case_quality(self, test_cases, requirement):
        """评估测试用例质量"""
        quality_scores = []

        for test_case in test_cases:
            score = 0.0

            # 完整性检查
            required_fields = ['title', 'steps', 'expected_result']
            completeness = sum(1 for field in required_fields
                             if field in test_case and test_case[field])
            score += (completeness / len(required_fields)) * 0.3

            # 相关性检查（使用语义相似度）
            case_text = f"{test_case.get('title', '')} {test_case.get('objective', '')}"
            similarity = self._calculate_semantic_similarity(case_text, requirement)
            score += similarity * 0.4

            # 可执行性检查（步骤的详细程度）
            steps = test_case.get('steps', [])
            if steps:
                avg_step_length = np.mean([len(step) for step in steps])
                executability = min(avg_step_length / 50, 1.0)  # 标准化到0-1
                score += executability * 0.3

            quality_scores.append(score)

        return quality_scores

```
### 7.2 需求覆盖分析

```python
class RequirementCoverageAnalyzer:
    def __init__(self, retriever, metadata_db):
        self.retriever = retriever
        self.metadata_db = metadata_db

    def analyze_coverage(self, requirement_id):
        """分析需求覆盖情况"""
        # 1. 获取需求详情
        requirement = self.metadata_db.get_requirement(requirement_id)

        # 2. 检索相关测试用例
        related_test_cases = self.retriever.search(
            requirement['description'],
            collection='test_cases',
            top_k=20
        )

        # 3. 计算覆盖度
        coverage_analysis = self._calculate_coverage_metrics(
            requirement, related_test_cases
        )

        # 4. 识别覆盖缺口
        coverage_gaps = self._identify_coverage_gaps(
            requirement, related_test_cases
        )

        return {
            'requirement_id': requirement_id,
            'coverage_metrics': coverage_analysis,
            'coverage_gaps': coverage_gaps,
            'recommendations': self._generate_recommendations(coverage_gaps)
        }

    def _calculate_coverage_metrics(self, requirement, test_cases):
        """计算覆盖度指标"""
        metrics = {
            'total_test_cases': len(test_cases),
            'direct_coverage': 0,
            'indirect_coverage': 0,
            'coverage_score': 0.0
        }

        req_keywords = self._extract_keywords(requirement['description'])

        for test_case in test_cases:
            case_text = f"{test_case['title']} {test_case['description']}"
            case_keywords = self._extract_keywords(case_text)

            # 计算关键词重叠度
            keyword_overlap = len(req_keywords & case_keywords) / len(req_keywords)

            if keyword_overlap > 0.7:
                metrics['direct_coverage'] += 1
            elif keyword_overlap > 0.3:
                metrics['indirect_coverage'] += 1

        # 计算总体覆盖分数
        total_coverage = metrics['direct_coverage'] + metrics['indirect_coverage'] * 0.5
        metrics['coverage_score'] = min(total_coverage / 5, 1.0)  # 标准化到0-1

        return metrics

    def generate_coverage_matrix(self, project_id):
        """生成需求-测试用例覆盖矩阵"""
        requirements = self.metadata_db.get_requirements_by_project(project_id)
        test_cases = self.metadata_db.get_test_cases_by_project(project_id)

        matrix = []
        for req in requirements:
            row = {'requirement_id': req['id'], 'requirement_title': req['title']}

            for test_case in test_cases:
                # 计算需求与测试用例的相关性
                similarity = self._calculate_semantic_similarity(
                    req['description'],
                    test_case['description']
                )

                # 设置覆盖阈值
                if similarity > 0.7:
                    coverage_status = 'full'
                elif similarity > 0.4:
                    coverage_status = 'partial'
                else:
                    coverage_status = 'none'

                row[f"tc_{test_case['id']}"] = {
                    'status': coverage_status,
                    'similarity': similarity
                }

            matrix.append(row)

        return matrix

```
## 8. 部署与运维建议

### 8.1 容器化部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/testdb
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
    depends_on:
      - postgres
      - redis
      - qdrant
    volumes:
      - ./data:/app/data

  postgres:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=testdb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
```

### 8.2 监控与告警

```python
class SystemMonitor:
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()

    def setup_monitoring(self):
        """设置系统监控"""
        # 数据库连接监控
        self.monitor_database_health()

        # 向量检索性能监控
        self.monitor_search_performance()

        # 内存使用监控
        self.monitor_memory_usage()

        # 磁盘空间监控
        self.monitor_disk_usage()

    def monitor_database_health(self):
        """数据库健康监控"""
        try:
            # 检查PostgreSQL连接
            pg_status = self._check_postgres_connection()

            # 检查Redis连接
            redis_status = self._check_redis_connection()

            # 检查向量数据库连接
            vector_db_status = self._check_vector_db_connection()

            if not all([pg_status, redis_status, vector_db_status]):
                self.alert_manager.send_alert(
                    "数据库连接异常",
                    f"PG: {pg_status}, Redis: {redis_status}, VectorDB: {vector_db_status}"
                )
        except Exception as e:
            self.alert_manager.send_alert("监控系统异常", str(e))

### 8.3 备份与恢复策略

```python
class BackupManager:
    def __init__(self):
        self.backup_config = {
            'postgres': {
                'schedule': '0 2 * * *',  # 每天凌晨2点
                'retention_days': 30
            },
            'vector_index': {
                'schedule': '0 3 * * *',  # 每天凌晨3点
                'retention_days': 7
            },
            'documents': {
                'schedule': '0 1 * * 0',  # 每周日凌晨1点
                'retention_days': 90
            }
        }

    def backup_postgres(self):
        """备份PostgreSQL数据"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_postgres_{timestamp}.sql"

        cmd = [
            'pg_dump',
            '-h', 'localhost',
            '-U', 'user',
            '-d', 'testdb',
            '-f', backup_file
        ]

        subprocess.run(cmd, check=True)

        # 压缩备份文件
        with open(backup_file, 'rb') as f_in:
            with gzip.open(f"{backup_file}.gz", 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)

        os.remove(backup_file)

        return f"{backup_file}.gz"

    def backup_vector_index(self):
        """备份向量索引"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = f"backup_vector_{timestamp}"

        # 创建备份目录
        os.makedirs(backup_dir, exist_ok=True)

        # 备份FAISS索引文件
        if os.path.exists('faiss_index.bin'):
            shutil.copy2('faiss_index.bin', backup_dir)

        # 备份映射文件
        if os.path.exists('id_mapping.json'):
            shutil.copy2('id_mapping.json', backup_dir)

        # 压缩备份目录
        shutil.make_archive(backup_dir, 'zip', backup_dir)
        shutil.rmtree(backup_dir)

        return f"{backup_dir}.zip"

```



